@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apple Design System for Card Management Dashboard */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@layer base {
  :root {
    /* Modern Neutral Color Palette */
    --indigo-50: 238 94% 98%;
    --indigo-100: 232 94% 95%;
    --indigo-600: 239 84% 67%;
    --purple-50: 270 100% 98%;
    --purple-600: 262 83% 58%;
    
    /* Modern Gray Scale */
    --gray-25: 210 40% 99%;
    --gray-50: 210 40% 98%;
    --gray-100: 210 40% 96%;
    --gray-200: 214 32% 91%;
    --gray-300: 213 27% 84%;
    --gray-400: 215 20% 65%;
    --gray-500: 215 16% 47%;
    --gray-600: 215 19% 35%;
    --gray-700: 215 25% 27%;
    --gray-800: 217 33% 17%;
    --gray-900: 222 84% 5%;
    
    /* Modern System Colors */
    --emerald-500: 158 64% 52%;
    --amber-500: 45 96% 64%;
    --rose-500: 349 89% 60%;
    --violet-500: 262 83% 58%;
    --pink-500: 330 81% 60%;
    --yellow-500: 48 96% 53%;
    
    /* Modern Design System Variables */
    --background: 210 40% 98%;
    --foreground: var(--gray-900);
    
    --card: 0 0% 100%;
    --card-foreground: var(--gray-900);
    
    --popover: 0 0% 100%;
    --popover-foreground: var(--gray-900);
    
    --primary: var(--indigo-600);
    --primary-foreground: 0 0% 100%;
    
    --secondary: var(--gray-100);
    --secondary-foreground: var(--gray-700);
    
    --muted: var(--gray-100);
    --muted-foreground: var(--gray-500);
    
    --accent: var(--indigo-50);
    --accent-foreground: var(--indigo-600);
    
    --success: var(--emerald-500);
    --success-foreground: 0 0% 100%;
    
    --warning: var(--amber-500);
    --warning-foreground: 0 0% 100%;
    
    --destructive: var(--rose-500);
    --destructive-foreground: 0 0% 100%;
    
    --border: var(--gray-200);
    --input: var(--gray-200);
    --ring: var(--indigo-600);
    
    /* Modern Radius System */
    --radius-sm: 8px;
    --radius: 12px;
    --radius-md: 16px;
    --radius-lg: 20px;
    --radius-xl: 24px;
    
    /* Modern Soft Shadows */
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.12), 0 1px 2px 0 rgb(0 0 0 / 0.24);
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06);
    --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
    --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
    
    /* Apple Blur Effects */
    --blur-glass: blur(20px);
    --blur-overlay: blur(8px);
    
    /* Apple Transitions */
    --transition-apple: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --duration-fast: 0.15s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;
  }
  
  .dark {
    --background: var(--gray-900);
    --foreground: var(--gray-50);
    
    --card: var(--gray-800);
    --card-foreground: var(--gray-50);
    
    --popover: var(--gray-800);
    --popover-foreground: var(--gray-50);
    
    --primary: var(--apple-blue);
    --primary-foreground: 0 0% 100%;
    
    --secondary: var(--gray-700);
    --secondary-foreground: var(--gray-200);
    
    --muted: var(--gray-700);
    --muted-foreground: var(--gray-400);
    
    --accent: var(--gray-700);
    --accent-foreground: var(--gray-50);
    
    --border: var(--gray-700);
    --input: var(--gray-700);
    --ring: var(--apple-blue);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  /* Apple System Font Stack */
  .font-sf {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  }
}

@layer components {
  /* Apple Glass Effect */
  .glass {
    @apply backdrop-blur-xl bg-white/80 border border-white/20;
    backdrop-filter: var(--blur-glass);
  }
  
  .glass-dark {
    @apply backdrop-blur-xl bg-gray-900/80 border border-gray-700/20;
    backdrop-filter: var(--blur-glass);
  }
  
  /* Modern Card Style */
  .modern-card {
    @apply bg-card rounded-3xl border-0 shadow-sm;
    box-shadow: var(--shadow);
  }
  
  .modern-card-hover {
    @apply modern-card transition-all duration-300 hover:shadow-md hover:-translate-y-1;
    transition-timing-function: var(--transition-apple);
  }
  
  /* Apple Button Styles */
  .btn-apple {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-all duration-300;
    transition-timing-function: var(--transition-apple);
  }
  
  .btn-primary {
    @apply btn-apple bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm;
  }
  
  .btn-secondary {
    @apply btn-apple bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }
  
  .btn-ghost {
    @apply btn-apple hover:bg-accent hover:text-accent-foreground;
  }
  
  /* Apple Input Styles */
  .input-apple {
    @apply flex h-10 w-full rounded-lg border border-input bg-card px-3 py-2 text-sm;
    @apply ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium;
    @apply placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2;
    @apply focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    transition: all var(--duration-normal) var(--transition-apple);
  }
  
  /* Apple Animations */
  .fade-in {
    animation: fadeIn var(--duration-normal) var(--transition-apple);
  }
  
  .slide-up {
    animation: slideUp var(--duration-normal) var(--transition-apple);
  }
  
  .scale-in {
    animation: scaleIn var(--duration-fast) var(--transition-apple);
  }
}

@layer utilities {
  /* Custom animations keyframes */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideUp {
    from { 
      opacity: 0; 
      transform: translateY(20px); 
    }
    to { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }
  
  @keyframes scaleIn {
    from { 
      opacity: 0; 
      transform: scale(0.95); 
    }
    to { 
      opacity: 1; 
      transform: scale(1); 
    }
  }
  
  /* Custom spacing */
  .space-apple {
    @apply space-y-6;
  }
  
  /* Apple specific margins and paddings */
  .p-apple {
    @apply p-6;
  }
  
  .px-apple {
    @apply px-6;
  }
  
  .py-apple {
    @apply py-4;
  }
}