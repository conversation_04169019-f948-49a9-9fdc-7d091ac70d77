@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apple Design System for Card Management Dashboard */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@layer base {
  :root {
    /* Modern Neutral Color Palette - Updated for breathable design */
    --background: 248 250 252; /* #F8FAFC - Light gray background */
    --foreground: 15 23 42; /* #0F172A - Dark text */

    /* Primary Purple Gradient System */
    --primary: 139 92 246; /* #8B5CF6 - Purple primary */
    --primary-foreground: 255 255 255; /* White text on primary */

    /* Modern Indigo Accents */
    --indigo-50: 238 94% 98%;
    --indigo-100: 232 94% 95%;
    --indigo-500: 99 102 241; /* #6366F1 - Modern indigo */
    --indigo-600: 79 70 229; /* #4F46E5 - Darker indigo */

    /* Purple Gradient System */
    --purple-50: 250 245 255; /* #FAF5FF */
    --purple-100: 243 232 255; /* #F3E8FF */
    --purple-500: 168 85 247; /* #A855F7 */
    --purple-600: 147 51 234; /* #9333EA */

    /* Enhanced Gray Scale for Modern UI */
    --gray-25: 249 250 251; /* #F9FAFB - Ultra light */
    --gray-50: 248 250 252; /* #F8FAFC - Very light */
    --gray-100: 241 245 249; /* #F1F5F9 - Light */
    --gray-200: 226 232 240; /* #E2E8F0 - Light border */
    --gray-300: 203 213 225; /* #CBD5E1 - Medium border */
    --gray-400: 148 163 184; /* #94A3B8 - Muted text */
    --gray-500: 100 116 139; /* #64748B - Secondary text */
    --gray-600: 71 85 105; /* #475569 - Primary text */
    --gray-700: 51 65 85; /* #334155 - Dark text */
    --gray-800: 30 41 59; /* #1E293B - Very dark */
    --gray-900: 15 23 42; /* #0F172A - Darkest */

    /* Modern System Colors - Softer palette */
    --emerald-500: 16 185 129; /* #10B981 - Success green */
    --amber-500: 245 158 11; /* #F59E0B - Warning amber */
    --rose-500: 239 68 68; /* #EF4444 - Error red */
    --violet-500: 139 92 246; /* #8B5CF6 - Accent violet */
    --pink-500: 236 72 153; /* #EC4899 - Accent pink */
    --blue-500: 59 130 246; /* #3B82F6 - Info blue */
    
    /* Modern Design System Variables - Updated */
    --card: 255 255 255; /* Pure white cards */
    --card-foreground: 15 23 42; /* Dark text on cards */

    --popover: 255 255 255;
    --popover-foreground: 15 23 42;

    --secondary: 241 245 249; /* Light gray secondary */
    --secondary-foreground: 51 65 85;

    --muted: 248 250 252; /* Very light muted */
    --muted-foreground: 100 116 139;

    --accent: 250 245 255; /* Light purple accent */
    --accent-foreground: 147 51 234;

    --success: 16 185 129;
    --success-foreground: 255 255 255;

    --warning: 245 158 11;
    --warning-foreground: 255 255 255;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 226 232 240; /* Softer borders */
    --input: 226 232 240;
    --ring: 139 92 246; /* Purple focus ring */

    /* Modern Radius System - Larger for breathable design */
    --radius-sm: 12px;
    --radius: 16px;
    --radius-md: 20px;
    --radius-lg: 24px;
    --radius-xl: 32px;

    /* Modern Soft Shadows - More subtle */
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.08), 0 1px 2px 0 rgb(0 0 0 / 0.06);
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.07), 0 2px 4px -1px rgb(0 0 0 / 0.04);
    --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.08), 0 4px 6px -2px rgb(0 0 0 / 0.03);
    --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.08), 0 10px 10px -5px rgb(0 0 0 / 0.02);
    
    /* Apple Blur Effects */
    --blur-glass: blur(20px);
    --blur-overlay: blur(8px);
    
    /* Apple Transitions */
    --transition-apple: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --duration-fast: 0.15s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;
  }
  
  .dark {
    --background: var(--gray-900);
    --foreground: var(--gray-50);
    
    --card: var(--gray-800);
    --card-foreground: var(--gray-50);
    
    --popover: var(--gray-800);
    --popover-foreground: var(--gray-50);
    
    --primary: var(--apple-blue);
    --primary-foreground: 0 0% 100%;
    
    --secondary: var(--gray-700);
    --secondary-foreground: var(--gray-200);
    
    --muted: var(--gray-700);
    --muted-foreground: var(--gray-400);
    
    --accent: var(--gray-700);
    --accent-foreground: var(--gray-50);
    
    --border: var(--gray-700);
    --input: var(--gray-700);
    --ring: var(--apple-blue);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  /* Apple System Font Stack */
  .font-sf {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  }
}

@layer components {
  /* Apple Glass Effect */
  .glass {
    @apply backdrop-blur-xl bg-white/80 border border-white/20;
    backdrop-filter: var(--blur-glass);
  }
  
  .glass-dark {
    @apply backdrop-blur-xl bg-gray-900/80 border border-gray-700/20;
    backdrop-filter: var(--blur-glass);
  }
  
  /* Modern Card Styles - Enhanced for breathable design */
  .modern-card {
    @apply bg-card rounded-3xl border-0 shadow-sm;
    box-shadow: var(--shadow);
  }

  .modern-card-hover {
    @apply modern-card transition-all duration-300 hover:shadow-md hover:-translate-y-1;
    transition-timing-function: var(--transition-apple);
  }

  /* Modern Stat Card */
  .modern-stat-card {
    @apply bg-white rounded-3xl p-8 shadow-sm hover:shadow-md transition-all duration-300 border-0;
    transition-timing-function: var(--transition-apple);
  }

  /* Modern Table Styles */
  .modern-table {
    @apply w-full border-collapse;
  }

  .modern-table th {
    @apply text-left py-4 text-sm font-medium text-gray-500 border-0;
  }

  .modern-table td {
    @apply py-4 border-0;
  }

  .modern-table tr:hover {
    @apply bg-gray-25 transition-colors duration-300;
  }

  /* Modern Search Input */
  .modern-search {
    @apply pl-10 pr-4 py-3 bg-gray-50 border-0 rounded-2xl text-sm;
    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white;
    @apply transition-all duration-300;
  }
  
  /* Apple Button Styles */
  .btn-apple {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-all duration-300;
    transition-timing-function: var(--transition-apple);
  }
  
  .btn-primary {
    @apply btn-apple bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm;
  }
  
  .btn-secondary {
    @apply btn-apple bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }
  
  .btn-ghost {
    @apply btn-apple hover:bg-accent hover:text-accent-foreground;
  }
  
  /* Apple Input Styles */
  .input-apple {
    @apply flex h-10 w-full rounded-lg border border-input bg-card px-3 py-2 text-sm;
    @apply ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium;
    @apply placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2;
    @apply focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    transition: all var(--duration-normal) var(--transition-apple);
  }
  
  /* Apple Animations */
  .fade-in {
    animation: fadeIn var(--duration-normal) var(--transition-apple);
  }
  
  .slide-up {
    animation: slideUp var(--duration-normal) var(--transition-apple);
  }
  
  .scale-in {
    animation: scaleIn var(--duration-fast) var(--transition-apple);
  }
}

@layer utilities {
  /* Custom animations keyframes */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideUp {
    from { 
      opacity: 0; 
      transform: translateY(20px); 
    }
    to { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }
  
  @keyframes scaleIn {
    from { 
      opacity: 0; 
      transform: scale(0.95); 
    }
    to { 
      opacity: 1; 
      transform: scale(1); 
    }
  }
  
  /* Custom spacing */
  .space-apple {
    @apply space-y-6;
  }
  
  /* Apple specific margins and paddings */
  .p-apple {
    @apply p-6;
  }
  
  .px-apple {
    @apply px-6;
  }
  
  .py-apple {
    @apply py-4;
  }
}