import React from 'react';
import { Line<PERSON><PERSON>, Line, XA<PERSON>s, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const data = [
  { name: '1月1日', revenue: 12000, orders: 45 },
  { name: '1月2日', revenue: 15000, orders: 52 },
  { name: '1月3日', revenue: 18000, orders: 68 },
  { name: '1月4日', revenue: 14000, orders: 48 },
  { name: '1月5日', revenue: 22000, orders: 78 },
  { name: '1月6日', revenue: 25000, orders: 89 },
  { name: '1月7日', revenue: 21000, orders: 72 },
  { name: '1月8日', revenue: 28000, orders: 95 },
  { name: '1月9日', revenue: 32000, orders: 112 },
  { name: '1月10日', revenue: 29000, orders: 98 },
  { name: '1月11日', revenue: 35000, orders: 125 },
  { name: '1月12日', revenue: 38000, orders: 134 },
  { name: '1月13日', revenue: 42000, orders: 148 },
  { name: '1月14日', revenue: 39000, orders: 142 },
  { name: '1月15日', revenue: 45000, orders: 156 },
];

export function RevenueChart() {
  return (
    <div className="w-full h-64">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
          <XAxis 
            dataKey="name" 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#6B7280' }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#6B7280' }}
            tickFormatter={(value) => `¥${(value / 1000).toFixed(0)}k`}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: '#FFFFFF',
              border: '1px solid #E5E7EB',
              borderRadius: '8px',
              boxShadow: '0 4px 16px rgba(0, 0, 0, 0.12)',
              fontSize: '14px'
            }}
            formatter={(value: number, name: string) => [
              name === 'revenue' ? `¥${value.toLocaleString()}` : value,
              name === 'revenue' ? '收入' : '订单数'
            ]}
            labelStyle={{ color: '#374151', fontWeight: 'medium' }}
          />
          <Line 
            type="monotone" 
            dataKey="revenue" 
            stroke="#007AFF" 
            strokeWidth={3}
            dot={{ fill: '#007AFF', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: '#007AFF', strokeWidth: 2, fill: '#FFFFFF' }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}