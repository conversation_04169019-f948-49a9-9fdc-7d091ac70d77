import React from 'react';
import { Line<PERSON><PERSON>, Line, <PERSON>A<PERSON>s, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Toolt<PERSON>, ResponsiveContainer } from 'recharts';

const data = [
  { name: '1月1日', revenue: 12000, orders: 45 },
  { name: '1月2日', revenue: 15000, orders: 52 },
  { name: '1月3日', revenue: 18000, orders: 68 },
  { name: '1月4日', revenue: 14000, orders: 48 },
  { name: '1月5日', revenue: 22000, orders: 78 },
  { name: '1月6日', revenue: 25000, orders: 89 },
  { name: '1月7日', revenue: 21000, orders: 72 },
  { name: '1月8日', revenue: 28000, orders: 95 },
  { name: '1月9日', revenue: 32000, orders: 112 },
  { name: '1月10日', revenue: 29000, orders: 98 },
  { name: '1月11日', revenue: 35000, orders: 125 },
  { name: '1月12日', revenue: 38000, orders: 134 },
  { name: '1月13日', revenue: 42000, orders: 148 },
  { name: '1月14日', revenue: 39000, orders: 142 },
  { name: '1月15日', revenue: 45000, orders: 156 },
];

export function RevenueChart() {
  return (
    <div className="w-full h-64">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" opacity={0.5} />
          <XAxis
            dataKey="name"
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#64748B' }}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#64748B' }}
            tickFormatter={(value) => `¥${(value / 1000).toFixed(0)}k`}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: '#FFFFFF',
              border: '1px solid #E2E8F0',
              borderRadius: '16px',
              boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.08), 0 4px 6px -2px rgb(0 0 0 / 0.03)',
              fontSize: '14px',
              padding: '12px 16px'
            }}
            formatter={(value: number, name: string) => [
              name === 'revenue' ? `¥${value.toLocaleString()}` : value,
              name === 'revenue' ? '收入' : '订单数'
            ]}
            labelStyle={{ color: '#1E293B', fontWeight: 'medium' }}
          />
          <Line
            type="monotone"
            dataKey="revenue"
            stroke="#8B5CF6"
            strokeWidth={2.5}
            dot={{ fill: '#8B5CF6', strokeWidth: 2, r: 3 }}
            activeDot={{ r: 5, stroke: '#8B5CF6', strokeWidth: 2, fill: '#FFFFFF' }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}