import React from 'react';
import { LucideIcon } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: LucideIcon;
}

export function StatsCard({ title, value, change, changeType, icon: Icon }: StatsCardProps) {
  const changeColor = {
    positive: 'text-emerald-600',
    negative: 'text-rose-500',
    neutral: 'text-gray-500'
  }[changeType];

  const changeIcon = {
    positive: '↗',
    negative: '↘',
    neutral: '→'
  }[changeType];

  return (
    <div className="bg-white rounded-3xl p-8 shadow-sm hover:shadow-md transition-all duration-300 border-0 group">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-500 mb-2">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors">
            {value}
          </p>
          <div className="flex items-center">
            <span className={`inline-flex items-center text-sm font-medium ${changeColor}`}>
              <span className="mr-1">{changeIcon}</span>
              {change}
            </span>
          </div>
        </div>
        <div className="p-3 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl group-hover:from-purple-100 group-hover:to-indigo-100 transition-all duration-300">
          <Icon className="w-6 h-6 text-purple-600 group-hover:text-purple-700 transition-colors" />
        </div>
      </div>
    </div>
  );
}