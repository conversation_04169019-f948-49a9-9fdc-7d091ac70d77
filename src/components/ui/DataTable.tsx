import React from 'react';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';

interface Column {
  key: string;
  title: string;
  width?: string;
  render?: (value: any, record: any) => React.ReactNode;
}

interface DataTableProps {
  columns: Column[];
  data: any[];
  loading?: boolean;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number) => void;
  };
}

export function DataTable({ columns, data, loading, pagination }: DataTableProps) {
  return (
    <div className="apple-card">
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-border">
              {columns.map((column) => (
                <th
                  key={column.key}
                  className="text-left py-3 px-4 font-medium text-sm text-muted-foreground"
                  style={{ width: column.width }}
                >
                  {column.title}
                </th>
              ))}
              <th className="text-left py-3 px-4 font-medium text-sm text-muted-foreground w-12">
                操作
              </th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={columns.length + 1} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                    <span className="ml-2 text-muted-foreground">加载中...</span>
                  </div>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={columns.length + 1} className="text-center py-8 text-muted-foreground">
                  暂无数据
                </td>
              </tr>
            ) : (
              data.map((record, index) => (
                <tr
                  key={index}
                  className="border-b border-border hover:bg-gray-50 transition-colors duration-apple-fast"
                >
                  {columns.map((column) => (
                    <td key={column.key} className="py-3 px-4 text-sm">
                      {column.render
                        ? column.render(record[column.key], record)
                        : record[column.key]}
                    </td>
                  ))}
                  <td className="py-3 px-4">
                    <button className="p-1 rounded hover:bg-gray-200 transition-colors duration-apple-fast">
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="flex items-center justify-between p-4 border-t border-border">
          <div className="text-sm text-muted-foreground">
            共 {pagination.total} 条记录
          </div>
          <div className="flex items-center space-x-2">
            <button
              disabled={pagination.current === 1}
              onClick={() => pagination.onChange(pagination.current - 1)}
              className="p-2 rounded-apple hover:bg-secondary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-apple-fast"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
            
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.ceil(pagination.total / pagination.pageSize) }).map((_, i) => {
                const page = i + 1;
                const isActive = page === pagination.current;
                
                return (
                  <button
                    key={page}
                    onClick={() => pagination.onChange(page)}
                    className={`w-8 h-8 rounded-apple text-sm font-medium transition-colors duration-apple-fast ${
                      isActive
                        ? 'bg-primary text-primary-foreground'
                        : 'hover:bg-secondary'
                    }`}
                  >
                    {page}
                  </button>
                );
              })}
            </div>

            <button
              disabled={pagination.current === Math.ceil(pagination.total / pagination.pageSize)}
              onClick={() => pagination.onChange(pagination.current + 1)}
              className="p-2 rounded-apple hover:bg-secondary disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-apple-fast"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}