import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  BarChart3, 
  CreditCard, 
  ShoppingCart, 
  Users, 
  DollarSign, 
  Settings,
  Menu,
  Home
} from 'lucide-react';
import logoImage from '../../assets/logo.png';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  isMobile?: boolean;
}

const navigationItems = [
  { name: '仪表板', href: '/', icon: Home },
  { name: '发卡管理', href: '/cards', icon: CreditCard },
  { name: '订单管理', href: '/orders', icon: ShoppingCart },
  { name: '用户管理', href: '/users', icon: Users },
  { name: '财务报表', href: '/finance', icon: DollarSign },
  { name: '系统设置', href: '/settings', icon: Settings },
];

export function Sidebar({ isOpen, onToggle, isMobile }: SidebarProps) {
  const location = useLocation();

  return (
    <div className="h-screen bg-card border-r border-border flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          {isOpen && (
            <div className="flex items-center space-x-3">
              <img 
                src={logoImage} 
                alt="发卡系统Logo" 
                className="w-8 h-8 rounded-xl shadow-sm"
              />
              <div>
                <h1 className="font-sf font-semibold text-lg text-gray-900">发卡系统</h1>
                <p className="text-sm text-gray-500">管理面板</p>
              </div>
            </div>
          )}
          <button
            onClick={onToggle}
            className="p-2 rounded-xl hover:bg-gray-50 transition-colors duration-300"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => {
          const isActive = location.pathname === item.href;
          const Icon = item.icon;
          
          return (
            <NavLink
              key={item.name}
              to={item.href}
              className={`
                flex items-center rounded-2xl px-4 py-3 text-sm font-medium transition-all duration-300
                ${isActive 
                  ? 'bg-indigo-50 text-indigo-600 border border-indigo-100' 
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }
                ${!isOpen ? 'justify-center' : ''}
              `}
            >
              <Icon className={`w-5 h-5 ${isOpen ? 'mr-4' : ''} ${isActive ? 'text-indigo-600' : ''}`} />
              {isOpen && <span>{item.name}</span>}
            </NavLink>
          );
        })}
      </nav>

      {/* Upgrade Card */}
      {isOpen && (
        <div className="p-4">
          <div className="p-6 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-2xl text-white">
            <div className="text-sm font-medium mb-2">Pro Mode</div>
            <div className="text-xs opacity-90 mb-4">
              升级到专业版解锁所有功能
            </div>
            <button className="w-full bg-white/20 hover:bg-white/30 rounded-xl py-2 text-sm font-medium transition-colors">
              立即升级 →
            </button>
          </div>
        </div>
      )}

      {/* Footer */}
      {isOpen && (
        <div className="p-4 border-t border-border">
          <div className="text-center text-xs text-gray-500">
            <p>发卡系统 v1.0</p>
            <p>© 2024 版权所有</p>
          </div>
        </div>
      )}
    </div>
  );
}