import React from 'react';
import { <PERSON>, Search, User, ChevronDown, Menu } from 'lucide-react';

interface HeaderProps {
  onMenuClick?: () => void;
  isMobile?: boolean;
}

export function Header({ onMenuClick, isMobile }: HeaderProps) {
  return (
    <header className="h-16 bg-white border-b border-gray-200 px-6 md:px-8 flex items-center justify-between">
      {/* Mobile Menu Button & Search */}
      <div className="flex items-center space-x-4 flex-1">
        {isMobile && (
          <button
            onClick={onMenuClick}
            className="p-2 rounded-xl hover:bg-gray-50 transition-colors duration-300"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>
        )}
        
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索..."
              className="w-full h-11 pl-11 pr-4 bg-gray-50 border-0 rounded-2xl text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:bg-white transition-all duration-300"
            />
          </div>
        </div>
      </div>

      {/* Right side */}
      <div className="flex items-center space-x-4">
        {/* Notifications */}
        <button className="relative p-2 rounded-apple hover:bg-secondary transition-colors duration-apple-fast">
          <Bell className="w-5 h-5" />
          <span className="absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full text-xs flex items-center justify-center">
            <span className="w-1.5 h-1.5 bg-white rounded-full"></span>
          </span>
        </button>

        {/* User Menu */}
        <div className="flex items-center space-x-3 p-2 rounded-apple hover:bg-secondary transition-colors duration-apple-fast cursor-pointer">
          <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
            <User className="w-4 h-4 text-primary-foreground" />
          </div>
          <div className="text-sm">
            <p className="font-medium">管理员</p>
            <p className="text-muted-foreground"><EMAIL></p>
          </div>
          <ChevronDown className="w-4 h-4 text-muted-foreground" />
        </div>
      </div>
    </header>
  );
}