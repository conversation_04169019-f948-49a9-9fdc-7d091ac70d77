import React, { useState } from 'react';
import { 
  TrendingUp, 
  Download, 
  Calendar,
  DollarSign,
  CreditCard,
  Pie<PERSON>hart,
  BarChart3
} from 'lucide-react';

export default function Finance() {
  const [dateRange, setDateRange] = useState('30days');
  
  const revenueData = [
    { period: '本日', revenue: '¥12,345', cost: '¥8,230', profit: '¥4,115', margin: '33.3%' },
    { period: '本周', revenue: '¥89,456', cost: '¥59,640', profit: '¥29,816', margin: '33.3%' },
    { period: '本月', revenue: '¥345,678', cost: '¥230,452', profit: '¥115,226', margin: '33.3%' },
    { period: '本年', revenue: '¥2,345,678', cost: '¥1,563,785', profit: '¥781,893', margin: '33.3%' },
  ];

  const paymentMethods = [
    { method: '支付宝', amount: '¥125,670', percentage: 45, transactions: 234 },
    { method: '微信支付', amount: '¥98,450', percentage: 35, transactions: 189 },
    { method: 'PayPal', amount: '¥42,380', percentage: 15, transactions: 67 },
    { method: '银行转账', amount: '¥14,120', percentage: 5, transactions: 23 },
  ];

  const topProducts = [
    { name: 'Steam卡密', revenue: '¥89,450', orders: 234, profit: '¥26,835' },
    { name: 'PSN卡密', revenue: '¥67,230', orders: 189, profit: '¥20,169' },
    { name: 'iTunes卡密', revenue: '¥45,120', orders: 156, profit: '¥13,536' },
    { name: 'Google Play卡密', revenue: '¥34,890', orders: 123, profit: '¥10,467' },
  ];

  return (
    <div className="space-apple animate-fade-in">
      {/* Page Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold font-sf mb-2">财务报表</h1>
          <p className="text-muted-foreground">查看详细的收入和支出分析</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select 
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="input-apple"
          >
            <option value="7days">最近7天</option>
            <option value="30days">最近30天</option>
            <option value="90days">最近90天</option>
            <option value="1year">最近1年</option>
          </select>
          <button className="btn-secondary">
            <Calendar className="w-4 h-4 mr-2" />
            自定义时间
          </button>
          <button className="btn-primary">
            <Download className="w-4 h-4 mr-2" />
            导出报表
          </button>
        </div>
      </div>

      {/* Revenue Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {revenueData.map((item, index) => (
          <div key={index} className="apple-card p-apple">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-600">{item.period}</h3>
              <TrendingUp className="w-5 h-5 text-success" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">收入</span>
                <span className="font-semibold text-success">{item.revenue}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">成本</span>
                <span className="font-semibold text-destructive">{item.cost}</span>
              </div>
              <div className="flex justify-between border-t pt-2">
                <span className="text-sm font-medium">利润</span>
                <span className="font-bold text-primary">{item.profit}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">利润率</span>
                <span className="font-semibold">{item.margin}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Revenue Trend */}
        <div className="apple-card p-apple">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold">收入趋势</h3>
            <BarChart3 className="w-5 h-5 text-muted-foreground" />
          </div>
          
          <div className="h-64 bg-gray-50 rounded-apple-md flex items-center justify-center">
            <div className="text-center">
              <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">收入趋势图表</p>
            </div>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="apple-card p-apple">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold">支付方式分析</h3>
            <PieChart className="w-5 h-5 text-muted-foreground" />
          </div>
          
          <div className="space-y-4">
            {paymentMethods.map((method, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 rounded-full" style={{ 
                    backgroundColor: ['#007AFF', '#34C759', '#FF9500', '#FF3B30'][index] 
                  }}></div>
                  <span className="font-medium">{method.method}</span>
                </div>
                <div className="text-right">
                  <p className="font-semibold">{method.amount}</p>
                  <p className="text-sm text-muted-foreground">{method.percentage}% · {method.transactions}笔</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Detailed Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Products */}
        <div className="apple-card p-apple">
          <h3 className="text-lg font-semibold mb-6">热销商品收入</h3>
          
          <div className="space-y-4">
            {topProducts.map((product, index) => (
              <div key={index} className="border-b border-border pb-4 last:border-b-0">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{product.name}</span>
                  <span className="font-bold text-success">{product.revenue}</span>
                </div>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>{product.orders} 订单</span>
                  <span>利润: {product.profit}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Financial Summary */}
        <div className="apple-card p-apple">
          <h3 className="text-lg font-semibold mb-6">财务摘要</h3>
          
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-success/5 rounded-apple-md">
                <DollarSign className="w-8 h-8 text-success mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">总收入</p>
                <p className="text-xl font-bold text-success">¥2,345,678</p>
              </div>
              <div className="text-center p-4 bg-destructive/5 rounded-apple-md">
                <CreditCard className="w-8 h-8 text-destructive mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">总支出</p>
                <p className="text-xl font-bold text-destructive">¥1,563,785</p>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-muted-foreground">净利润</span>
                <span className="text-2xl font-bold text-primary">¥781,893</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">利润率</span>
                <span className="font-semibold">33.3%</span>
              </div>
            </div>
            
            <div className="bg-primary/5 p-4 rounded-apple-md">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="w-4 h-4 text-primary" />
                <span className="font-medium text-primary">增长趋势</span>
              </div>
              <p className="text-sm text-muted-foreground">
                相比上月增长 +15.2%，表现优秀
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}