import React, { useState } from 'react';
import { DataTable } from '../components/ui/DataTable';
import { 
  Plus, 
  Download, 
  Upload, 
  Filter, 
  Search,
  MoreHorizontal,
  Edit,
  Trash2
} from 'lucide-react';

export default function Cards() {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  
  const cardData = [
    {
      id: 'CARD001',
      product: 'Steam卡密',
      value: '¥100',
      code: 'ABCD-EFGH-IJKL-MNOP',
      status: '未销售',
      created: '2024-01-15',
      sold: null
    },
    {
      id: 'CARD002',
      product: 'PSN卡密',
      value: '¥200',
      code: 'QRST-UVWX-YZAB-CDEF',
      status: '已销售',
      created: '2024-01-14',
      sold: '2024-01-16'
    },
    {
      id: 'CARD003',
      product: 'iTunes卡密',
      value: '¥50',
      code: 'GHIJ-KLMN-OPQR-STUV',
      status: '已使用',
      created: '2024-01-13',
      sold: '2024-01-15'
    },
    {
      id: 'CARD004',
      product: 'Google Play卡密',
      value: '¥300',
      code: 'WXYZ-1234-5678-9ABC',
      status: '未销售',
      created: '2024-01-12',
      sold: null
    },
  ];

  const columns = [
    {
      key: 'id',
      title: '卡密ID',
      width: '120px'
    },
    {
      key: 'product',
      title: '商品名称',
      width: '150px'
    },
    {
      key: 'value',
      title: '面值',
      width: '100px',
      render: (value: string) => (
        <span className="font-semibold text-primary">{value}</span>
      )
    },
    {
      key: 'code',
      title: '卡密',
      width: '200px',
      render: (code: string) => (
        <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
          {code}
        </span>
      )
    },
    {
      key: 'status',
      title: '状态',
      width: '100px',
      render: (status: string) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          status === '未销售' ? 'bg-gray-100 text-gray-800' :
          status === '已销售' ? 'bg-success/10 text-success' :
          status === '已使用' ? 'bg-primary/10 text-primary' :
          'bg-warning/10 text-warning'
        }`}>
          {status}
        </span>
      )
    },
    {
      key: 'created',
      title: '创建时间',
      width: '120px'
    },
    {
      key: 'sold',
      title: '销售时间',
      width: '120px',
      render: (sold: string | null) => sold || '-'
    }
  ];

  return (
    <div className="space-apple animate-fade-in">
      {/* Page Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold font-sf mb-2">发卡管理</h1>
          <p className="text-muted-foreground">管理您的所有卡密库存</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button className="btn-secondary">
            <Download className="w-4 h-4 mr-2" />
            导出
          </button>
          <button className="btn-secondary">
            <Upload className="w-4 h-4 mr-2" />
            导入
          </button>
          <button className="btn-primary">
            <Plus className="w-4 h-4 mr-2" />
            添加卡密
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="apple-card p-apple mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="搜索卡密或商品名称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-apple pl-10 w-full"
              />
            </div>
          </div>
          
          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select className="input-apple">
              <option value="">所有商品</option>
              <option value="steam">Steam卡密</option>
              <option value="psn">PSN卡密</option>
              <option value="itunes">iTunes卡密</option>
              <option value="googleplay">Google Play卡密</option>
            </select>
            
            <select className="input-apple">
              <option value="">所有状态</option>
              <option value="unsold">未销售</option>
              <option value="sold">已销售</option>
              <option value="used">已使用</option>
              <option value="expired">已过期</option>
            </select>
            
            <button className="btn-secondary">
              <Filter className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-primary">1,234</p>
            <p className="text-sm text-muted-foreground">总卡密数</p>
          </div>
        </div>
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-success">856</p>
            <p className="text-sm text-muted-foreground">已销售</p>
          </div>
        </div>
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-600">378</p>
            <p className="text-sm text-muted-foreground">未销售</p>
          </div>
        </div>
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-warning">12</p>
            <p className="text-sm text-muted-foreground">即将过期</p>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={cardData}
        pagination={{
          current: currentPage,
          total: 1000,
          pageSize: 20,
          onChange: setCurrentPage
        }}
      />
    </div>
  );
}