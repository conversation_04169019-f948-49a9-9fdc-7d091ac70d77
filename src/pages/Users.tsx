import React, { useState } from 'react';
import { DataTable } from '../components/ui/DataTable';
import { 
  UserPlus, 
  Download, 
  Filter, 
  Search,
  Mail,
  MessageSquare,
  Ban,
  CheckCircle
} from 'lucide-react';

export default function Users() {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  
  const userData = [
    {
      id: 'U001',
      name: '张三',
      email: 'zhang<PERSON>@example.com',
      role: 'VIP用户',
      status: '活跃',
      orders: 23,
      spent: '¥2,340',
      registered: '2024-01-10',
      lastLogin: '2024-01-16 15:30'
    },
    {
      id: 'U002',
      name: '李四',
      email: '<EMAIL>',
      role: '普通用户',
      status: '活跃',
      orders: 8,
      spent: '¥890',
      registered: '2024-01-12',
      lastLogin: '2024-01-16 14:20'
    },
    {
      id: 'U003',
      name: '王五',
      email: '<EMAIL>',
      role: '代理商',
      status: '已禁用',
      orders: 156,
      spent: '¥15,600',
      registered: '2023-12-05',
      lastLogin: '2024-01-15 09:45'
    },
    {
      id: 'U004',
      name: '赵六',
      email: '<EMAIL>',
      role: '普通用户',
      status: '活跃',
      orders: 4,
      spent: '¥340',
      registered: '2024-01-14',
      lastLogin: '2024-01-16 11:15'
    },
  ];

  const columns = [
    {
      key: 'id',
      title: '用户ID',
      width: '80px',
      render: (id: string) => (
        <span className="font-mono text-sm">{id}</span>
      )
    },
    {
      key: 'name',
      title: '姓名',
      width: '120px',
      render: (name: string, record: any) => (
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
            <span className="text-primary-foreground text-sm font-medium">
              {name.charAt(0)}
            </span>
          </div>
          <div>
            <p className="font-medium">{name}</p>
            <p className="text-xs text-muted-foreground">{record.email}</p>
          </div>
        </div>
      )
    },
    {
      key: 'role',
      title: '用户类型',
      width: '100px',
      render: (role: string) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          role === 'VIP用户' ? 'bg-warning/10 text-warning' :
          role === '代理商' ? 'bg-purple-100 text-purple-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {role}
        </span>
      )
    },
    {
      key: 'status',
      title: '状态',
      width: '80px',
      render: (status: string) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          status === '活跃' ? 'bg-success/10 text-success' :
          status === '已禁用' ? 'bg-destructive/10 text-destructive' :
          'bg-gray-100 text-gray-800'
        }`}>
          {status}
        </span>
      )
    },
    {
      key: 'orders',
      title: '订单数',
      width: '80px',
      render: (orders: number) => (
        <span className="font-semibold">{orders}</span>
      )
    },
    {
      key: 'spent',
      title: '消费金额',
      width: '100px',
      render: (spent: string) => (
        <span className="font-semibold text-success">{spent}</span>
      )
    },
    {
      key: 'registered',
      title: '注册时间',
      width: '120px'
    },
    {
      key: 'lastLogin',
      title: '最后登录',
      width: '150px',
      render: (lastLogin: string) => (
        <span className="text-sm text-muted-foreground">{lastLogin}</span>
      )
    }
  ];

  return (
    <div className="space-apple animate-fade-in">
      {/* Page Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold font-sf mb-2">用户管理</h1>
          <p className="text-muted-foreground">管理系统用户和权限</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button className="btn-secondary">
            <Download className="w-4 h-4 mr-2" />
            导出用户
          </button>
          <button className="btn-primary">
            <UserPlus className="w-4 h-4 mr-2" />
            添加用户
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-primary">1,456</p>
            <p className="text-sm text-muted-foreground">总用户数</p>
          </div>
        </div>
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-success">1,234</p>
            <p className="text-sm text-muted-foreground">活跃用户</p>
          </div>
        </div>
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-warning">89</p>
            <p className="text-sm text-muted-foreground">VIP用户</p>
          </div>
        </div>
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">23</p>
            <p className="text-sm text-muted-foreground">代理商</p>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="apple-card p-apple mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="搜索用户名或邮箱..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-apple pl-10 w-full"
              />
            </div>
          </div>
          
          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select className="input-apple">
              <option value="">所有用户类型</option>
              <option value="normal">普通用户</option>
              <option value="vip">VIP用户</option>
              <option value="agent">代理商</option>
              <option value="admin">管理员</option>
            </select>
            
            <select className="input-apple">
              <option value="">所有状态</option>
              <option value="active">活跃</option>
              <option value="inactive">非活跃</option>
              <option value="banned">已禁用</option>
            </select>
            
            <button className="btn-secondary">
              <Filter className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="apple-card p-4 mb-6">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold">批量操作</h3>
          <div className="flex items-center space-x-2">
            <button className="btn-ghost text-sm">
              <Mail className="w-4 h-4 mr-1" />
              发送邮件
            </button>
            <button className="btn-ghost text-sm">
              <MessageSquare className="w-4 h-4 mr-1" />
              发送消息
            </button>
            <button className="btn-ghost text-sm text-destructive">
              <Ban className="w-4 h-4 mr-1" />
              禁用用户
            </button>
            <button className="btn-ghost text-sm text-success">
              <CheckCircle className="w-4 h-4 mr-1" />
              启用用户
            </button>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={userData}
        pagination={{
          current: currentPage,
          total: 1456,
          pageSize: 20,
          onChange: setCurrentPage
        }}
      />
    </div>
  );
}