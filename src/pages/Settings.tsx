import React, { useState } from 'react';
import { 
  Globe, 
  Mail, 
  Shield, 
  Palette, 
  CreditCard,
  Bell,
  Users,
  Database,
  Save
} from 'lucide-react';

export default function Settings() {
  const [activeTab, setActiveTab] = useState('general');
  
  const tabs = [
    { id: 'general', name: '基础设置', icon: Globe },
    { id: 'payment', name: '支付设置', icon: CreditCard },
    { id: 'email', name: '邮件设置', icon: Mail },
    { id: 'security', name: '安全设置', icon: Shield },
    { id: 'notifications', name: '通知设置', icon: Bell },
    { id: 'users', name: '用户权限', icon: Users },
    { id: 'appearance', name: '外观设置', icon: Palette },
    { id: 'backup', name: '数据备份', icon: Database },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">网站基本信息</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">网站名称</label>
                  <input type="text" defaultValue="发卡系统" className="input-apple w-full" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">网站域名</label>
                  <input type="text" defaultValue="cards.example.com" className="input-apple w-full" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">联系邮箱</label>
                  <input type="email" defaultValue="<EMAIL>" className="input-apple w-full" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">客服电话</label>
                  <input type="tel" defaultValue="+86 400-1234-567" className="input-apple w-full" />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">网站描述</label>
                <textarea 
                  rows={3} 
                  defaultValue="专业的数字卡密销售平台，提供安全可靠的购买体验。"
                  className="input-apple w-full resize-none"
                />
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">SEO设置</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">SEO标题</label>
                  <input type="text" defaultValue="发卡系统 - 专业数字卡密平台" className="input-apple w-full" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">关键词</label>
                  <input type="text" defaultValue="卡密,游戏充值,数字商品" className="input-apple w-full" />
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'payment':
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">支付接口配置</h3>
              
              {/* Alipay */}
              <div className="apple-card p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold">支</span>
                    </div>
                    <div>
                      <h4 className="font-semibold">支付宝</h4>
                      <p className="text-sm text-muted-foreground">支持即时到账</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" defaultChecked className="sr-only peer" />
                    <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                  </label>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">App ID</label>
                    <input type="text" placeholder="输入支付宝App ID" className="input-apple w-full" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">商户私钥</label>
                    <input type="password" placeholder="输入商户私钥" className="input-apple w-full" />
                  </div>
                </div>
              </div>
              
              {/* WeChat Pay */}
              <div className="apple-card p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold">微</span>
                    </div>
                    <div>
                      <h4 className="font-semibold">微信支付</h4>
                      <p className="text-sm text-muted-foreground">支持扫码支付</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" defaultChecked className="sr-only peer" />
                    <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                  </label>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">商户号</label>
                    <input type="text" placeholder="输入微信商户号" className="input-apple w-full" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">API密钥</label>
                    <input type="password" placeholder="输入API密钥" className="input-apple w-full" />
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">手续费设置</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">支付宝手续费 (%)</label>
                  <input type="number" defaultValue="0.6" step="0.1" className="input-apple w-full" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">微信手续费 (%)</label>
                  <input type="number" defaultValue="0.6" step="0.1" className="input-apple w-full" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">PayPal手续费 (%)</label>
                  <input type="number" defaultValue="2.9" step="0.1" className="input-apple w-full" />
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'security':
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">登录安全</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">启用两步验证</h4>
                    <p className="text-sm text-muted-foreground">增强账户安全性</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" />
                    <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                  </label>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">登录失败锁定</h4>
                    <p className="text-sm text-muted-foreground">5次失败后锁定账户</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" defaultChecked className="sr-only peer" />
                    <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                  </label>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">最大登录尝试次数</label>
                    <input type="number" defaultValue="5" className="input-apple w-full" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">锁定时间 (分钟)</label>
                    <input type="number" defaultValue="30" className="input-apple w-full" />
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">API安全</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">API访问限制</h4>
                    <p className="text-sm text-muted-foreground">限制API调用频率</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" defaultChecked className="sr-only peer" />
                    <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                  </label>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">每分钟请求限制</label>
                    <input type="number" defaultValue="100" className="input-apple w-full" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">每日请求限制</label>
                    <input type="number" defaultValue="10000" className="input-apple w-full" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
        
      default:
        return (
          <div className="text-center py-12">
            <p className="text-muted-foreground">该功能正在开发中...</p>
          </div>
        );
    }
  };

  return (
    <div className="space-apple animate-fade-in">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold font-sf mb-2">系统设置</h1>
        <p className="text-muted-foreground">配置系统参数和功能选项</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="apple-card p-4">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-apple-md text-left transition-all duration-apple-fast ${
                      activeTab === tab.id
                        ? 'bg-primary text-primary-foreground'
                        : 'hover:bg-secondary'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="apple-card p-apple">
            {renderTabContent()}
            
            {/* Save Button */}
            <div className="mt-8 pt-6 border-t border-border">
              <div className="flex items-center justify-end space-x-3">
                <button className="btn-secondary">取消</button>
                <button className="btn-primary">
                  <Save className="w-4 h-4 mr-2" />
                  保存设置
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}