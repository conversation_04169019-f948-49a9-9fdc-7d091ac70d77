import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { Home, AlertCircle } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center p-apple max-w-md mx-auto">
        <div className="w-24 h-24 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-6">
          <AlertCircle className="w-12 h-12 text-destructive" />
        </div>
        
        <h1 className="text-6xl font-bold font-sf mb-4 text-gray-900">404</h1>
        <h2 className="text-2xl font-semibold mb-4 text-gray-700">页面未找到</h2>
        <p className="text-muted-foreground mb-8">
          抱歉，您访问的页面不存在或已被移动。
        </p>
        
        <Link 
          to="/" 
          className="btn-primary inline-flex items-center"
        >
          <Home className="w-4 h-4 mr-2" />
          返回首页
        </Link>
        
        <div className="mt-8 pt-6 border-t border-border">
          <p className="text-sm text-muted-foreground">
            如果您认为这是一个错误，请联系系统管理员。
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
