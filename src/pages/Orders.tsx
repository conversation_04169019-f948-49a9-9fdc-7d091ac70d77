import React, { useState } from 'react';
import { DataTable } from '../components/ui/DataTable';
import { 
  Filter, 
  Search, 
  Download,
  Eye,
  RefreshCw
} from 'lucide-react';

export default function Orders() {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  
  const orderData = [
    {
      id: '#12345',
      customer: '张三',
      product: 'Steam卡密 ¥100',
      amount: '¥299',
      status: '已完成',
      payment: '支付宝',
      created: '2024-01-16 14:30',
      completed: '2024-01-16 14:32'
    },
    {
      id: '#12346',
      customer: '李四',
      product: 'PSN卡密 ¥200',
      amount: '¥399',
      status: '处理中',
      payment: '微信支付',
      created: '2024-01-16 13:25',
      completed: null
    },
    {
      id: '#12347',
      customer: '王五',
      product: 'iTunes卡密 ¥50',
      amount: '¥99',
      status: '已发货',
      payment: 'PayPal',
      created: '2024-01-16 12:15',
      completed: null
    },
    {
      id: '#12348',
      customer: '赵六',
      product: 'Google Play卡密 ¥300',
      amount: '¥599',
      status: '待支付',
      payment: '支付宝',
      created: '2024-01-16 11:45',
      completed: null
    },
  ];

  const columns = [
    {
      key: 'id',
      title: '订单号',
      width: '120px',
      render: (id: string) => (
        <span className="font-mono text-primary">{id}</span>
      )
    },
    {
      key: 'customer',
      title: '客户',
      width: '100px'
    },
    {
      key: 'product',
      title: '商品',
      width: '200px'
    },
    {
      key: 'amount',
      title: '金额',
      width: '100px',
      render: (amount: string) => (
        <span className="font-semibold text-success">{amount}</span>
      )
    },
    {
      key: 'status',
      title: '状态',
      width: '100px',
      render: (status: string) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          status === '已完成' ? 'bg-success/10 text-success' :
          status === '处理中' ? 'bg-warning/10 text-warning' :
          status === '已发货' ? 'bg-primary/10 text-primary' :
          status === '待支付' ? 'bg-gray-100 text-gray-800' :
          'bg-destructive/10 text-destructive'
        }`}>
          {status}
        </span>
      )
    },
    {
      key: 'payment',
      title: '支付方式',
      width: '100px'
    },
    {
      key: 'created',
      title: '创建时间',
      width: '150px'
    }
  ];

  return (
    <div className="space-apple animate-fade-in">
      {/* Page Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold font-sf mb-2">订单管理</h1>
          <p className="text-muted-foreground">管理所有订单和交易记录</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button className="btn-secondary">
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </button>
          <button className="btn-secondary">
            <Download className="w-4 h-4 mr-2" />
            导出
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-primary">2,456</p>
            <p className="text-sm text-muted-foreground">总订单</p>
          </div>
        </div>
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-success">1,890</p>
            <p className="text-sm text-muted-foreground">已完成</p>
          </div>
        </div>
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-warning">234</p>
            <p className="text-sm text-muted-foreground">处理中</p>
          </div>
        </div>
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-600">156</p>
            <p className="text-sm text-muted-foreground">待支付</p>
          </div>
        </div>
        <div className="apple-card p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-destructive">45</p>
            <p className="text-sm text-muted-foreground">已取消</p>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="apple-card p-apple mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="搜索订单号或客户..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-apple pl-10 w-full"
              />
            </div>
          </div>
          
          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select className="input-apple">
              <option value="">所有状态</option>
              <option value="completed">已完成</option>
              <option value="processing">处理中</option>
              <option value="shipped">已发货</option>
              <option value="pending">待支付</option>
              <option value="cancelled">已取消</option>
            </select>
            
            <select className="input-apple">
              <option value="">支付方式</option>
              <option value="alipay">支付宝</option>
              <option value="wechat">微信支付</option>
              <option value="paypal">PayPal</option>
              <option value="bank">银行转账</option>
            </select>
            
            <input
              type="date"
              className="input-apple"
              placeholder="开始日期"
            />
            
            <input
              type="date"
              className="input-apple"
              placeholder="结束日期"
            />
            
            <button className="btn-secondary">
              <Filter className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={orderData}
        pagination={{
          current: currentPage,
          total: 2456,
          pageSize: 20,
          onChange: setCurrentPage
        }}
      />
    </div>
  );
}