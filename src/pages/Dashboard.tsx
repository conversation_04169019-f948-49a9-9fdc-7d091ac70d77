import React from 'react';
import { StatsCard } from '../components/ui/StatsCard';
import { RevenueChart } from '../components/charts/RevenueChart';
import { ProductPieChart } from '../components/charts/ProductPieChart';
import { 
  DollarSign, 
  ShoppingCart, 
  Users, 
  Package, 
  TrendingUp,
  Activity,
  PieChart,
  Search
} from 'lucide-react';
import dashboardHero from '../assets/dashboard-hero.png';

export default function Dashboard() {
  const statsData = [
    {
      title: '今日销售',
      value: '¥12,345',
      change: '+12.5% 较昨日',
      changeType: 'positive' as const,
      icon: DollarSign
    },
    {
      title: '总订单数',
      value: '1,234',
      change: '+5.2% 较昨日',
      changeType: 'positive' as const,
      icon: ShoppingCart
    },
    {
      title: '活跃用户',
      value: '856',
      change: '+2.1% 较昨日',
      changeType: 'positive' as const,
      icon: Users
    },
    {
      title: '库存状态',
      value: '98.5%',
      change: '-0.5% 较昨日',
      changeType: 'negative' as const,
      icon: Package
    }
  ];

  const recentOrders = [
    { id: '#12345', customer: '张三', amount: '¥299', status: '已完成', time: '2分钟前' },
    { id: '#12346', customer: '李四', amount: '¥199', status: '处理中', time: '5分钟前' },
    { id: '#12347', customer: '王五', amount: '¥99', status: '已发货', time: '10分钟前' },
    { id: '#12348', customer: '赵六', amount: '¥399', status: '已完成', time: '15分钟前' },
  ];

  const topProducts = [
    { name: 'Steam卡密', sales: 245, revenue: '¥24,500' },
    { name: 'PSN卡密', sales: 189, revenue: '¥18,900' },
    { name: 'iTunes卡密', sales: 156, revenue: '¥15,600' },
    { name: 'Google Play卡密', sales: 134, revenue: '¥13,400' },
  ];

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">仪表板</h1>
            <p className="text-gray-600">欢迎回来，这是您的业务概览</p>
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-500">01 Feb 2025 - 01 Mar 2025</span>
            <button className="px-4 py-2 text-sm bg-gray-50 text-gray-600 rounded-xl hover:bg-gray-100 transition-colors border-0">
              Monthly ▼
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
        {statsData.map((stat, index) => (
          <div key={index} className="animate-slide-up" style={{ animationDelay: `${index * 0.1}s` }}>
            <StatsCard {...stat} />
          </div>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        {/* Revenue Chart */}
        <div className="lg:col-span-2 bg-white rounded-3xl p-8 shadow-sm border-0">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900">收入预测</h3>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm bg-gray-50 rounded-xl px-4 py-2">
                <button className="text-purple-600 font-medium">Monthly</button>
                <button className="text-gray-500 hover:text-gray-700 transition-colors">Quarterly</button>
                <button className="text-gray-500 hover:text-gray-700 transition-colors">Yearly</button>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <div className="text-3xl font-bold text-gray-900 mb-1">¥23,569.00</div>
            <div className="flex items-center text-sm">
              <span className="text-emerald-600 font-medium mr-2">↗ 6.2%</span>
              <span className="text-gray-500">from last period</span>
            </div>
          </div>

          <RevenueChart />
        </div>

        {/* Source Analytics */}
        <div className="bg-white rounded-3xl p-8 shadow-sm border-0">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900">数据来源</h3>
            <button className="text-sm text-gray-500 hover:text-gray-700 transition-colors">Weekly ▼</button>
          </div>
          
          <div className="space-y-6">
            <div>
              <div className="flex items-center justify-between mb-3">
                <span className="text-gray-600">获取量</span>
                <span className="font-semibold text-gray-900">¥12.01</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-2.5">
                <div className="bg-purple-500 h-2.5 rounded-full transition-all duration-500" style={{ width: '60%' }}></div>
              </div>
              <div className="text-xs text-emerald-600 mt-2 font-medium">↗ 1.1%</div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-3">
                <span className="text-gray-600">转化</span>
                <span className="font-semibold text-gray-900">1.2 days</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-2.5">
                <div className="bg-emerald-500 h-2.5 rounded-full transition-all duration-500" style={{ width: '40%' }}></div>
              </div>
              <div className="text-xs text-emerald-600 mt-2 font-medium">↗ 2.0%</div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-3">
                <span className="text-gray-600">ROI</span>
                <span className="font-semibold text-gray-900">36%</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-2.5">
                <div className="bg-amber-500 h-2.5 rounded-full transition-all duration-500" style={{ width: '75%' }}></div>
              </div>
              <div className="text-xs text-emerald-600 mt-2 font-medium">↗ 1.7%</div>
            </div>
          </div>
          
          <button className="w-full mt-6 text-sm text-indigo-600 hover:text-indigo-700 font-medium">
            查看报告
          </button>
        </div>
      </div>

      {/* Top Products Summary */}
      <div className="bg-white rounded-3xl p-8 shadow-sm border-0 mb-8">
        <h3 className="text-xl font-semibold text-gray-900 mb-6">热销商品概览</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {topProducts.map((product, index) => (
            <div key={index} className="p-6 bg-gray-50 rounded-2xl border-0 hover:bg-gray-100 transition-all duration-300 hover:shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <span className="font-medium text-gray-900">{product.name}</span>
                <span className="text-xs bg-purple-100 text-purple-600 px-3 py-1 rounded-xl font-medium">
                  #{index + 1}
                </span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">销量</span>
                  <span className="font-semibold text-gray-900">{product.sales}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">收入</span>
                  <span className="font-semibold text-emerald-600">{product.revenue}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Orders */}
      <div className="bg-white rounded-3xl p-8 shadow-sm border-0">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900">联系人</h3>
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search contact..."
                className="modern-search w-64"
              />
            </div>
            <button className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
              Filter
            </button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="modern-table">
            <thead>
              <tr>
                <th className="text-left py-4 text-sm font-medium text-gray-500">客户</th>
                <th className="text-left py-4 text-sm font-medium text-gray-500">邮箱</th>
                <th className="text-left py-4 text-sm font-medium text-gray-500">电话</th>
                <th className="text-left py-4 text-sm font-medium text-gray-500">状态</th>
                <th className="text-left py-4 text-sm font-medium text-gray-500">性别</th>
              </tr>
            </thead>
            <tbody>
              {recentOrders.map((order, index) => (
                <tr key={index} className="hover:bg-gray-50 transition-colors duration-300">
                  <td className="py-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        {order.customer.charAt(0)}
                      </div>
                      <span className="text-sm font-medium text-gray-900">{order.customer}</span>
                    </div>
                  </td>
                  <td className="py-4 text-sm text-gray-600"><EMAIL></td>
                  <td className="py-4 text-sm text-gray-600">(555) 123-4567</td>
                  <td className="py-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-lg text-xs font-medium ${
                      order.status === '已完成' ? 'bg-emerald-50 text-emerald-600' :
                      order.status === '处理中' ? 'bg-amber-50 text-amber-600' :
                      'bg-indigo-50 text-indigo-600'
                    }`}>
                      • {order.status === '已完成' ? 'New' : order.status === '处理中' ? 'Customer' : 'Partner'}
                    </span>
                  </td>
                  <td className="py-4 text-sm text-gray-600">
                    {index % 2 === 0 ? '♀ Female' : '♂ Male'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}